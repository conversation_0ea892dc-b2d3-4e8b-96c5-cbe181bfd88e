'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Target, 
  TrendingUp, 
  BookOpen, 
  Clock, 
  DollarSign, 
  Star,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  ExternalLink,
  Heart,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { EnhancedAssessmentResults as ResultsType } from '@/lib/enhancedAssessmentService';

interface EnhancedAssessmentResultsProps {
  assessmentId: string;
}

export default function EnhancedAssessmentResults({ assessmentId }: EnhancedAssessmentResultsProps) {
  const [results, setResults] = useState<ResultsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCareerPath, setSelectedCareerPath] = useState<string | null>(null);
  const [feedback, setFeedback] = useState<Record<string, any>>({});

  useEffect(() => {
    fetchEnhancedResults();
  }, [assessmentId]);

  const fetchEnhancedResults = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/assessment/${assessmentId}/enhanced-results`);
      const data = await response.json();

      if (data.success) {
        setResults(data.data);
        setSelectedCareerPath(data.data.careerPathRecommendations[0]?.id || null);
      } else {
        setError(data.error || 'Failed to load enhanced results');
      }
    } catch (err) {
      setError('Failed to load enhanced results');
      console.error('Error fetching enhanced results:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCareerPathFeedback = async (careerPathId: string, isHelpful: boolean) => {
    setFeedback(prev => ({
      ...prev,
      [careerPathId]: isHelpful
    }));

    try {
      await fetch(`/api/assessment/${assessmentId}/enhanced-results`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          careerPathFeedback: {
            [careerPathId]: isHelpful
          }
        })
      });
    } catch (err) {
      console.error('Error saving feedback:', err);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 space-y-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Generating your enhanced assessment results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Card className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-red-700 mb-2">Error Loading Results</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchEnhancedResults}>Try Again</Button>
        </Card>
      </div>
    );
  }

  if (!results) return null;

  const selectedCareer = results.careerPathRecommendations.find(
    cp => cp.id === selectedCareerPath
  );

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Your Enhanced Career Assessment Results
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Personalized insights, career paths, and learning recommendations
        </p>
      </div>

      {/* Overall Readiness Score */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-600" />
            Career Readiness Overview
          </h2>
          <Badge variant={results.insights.scores.readinessScore >= 70 ? 'default' : 'secondary'}>
            {results.insights.scores.readinessScore >= 70 ? 'High Readiness' : 
             results.insights.scores.readinessScore >= 50 ? 'Moderate Readiness' : 'Building Readiness'}
          </Badge>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-1">
              {results.insights.scores.readinessScore}%
            </div>
            <div className="text-sm text-gray-600">Overall Readiness</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">
              {results.careerPathRecommendations.length}
            </div>
            <div className="text-sm text-gray-600">Career Matches</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-1">
              {results.insights.estimatedTransitionTime}
            </div>
            <div className="text-sm text-gray-600">Est. Transition Time</div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h3 className="font-medium mb-2">Key Insights</h3>
          <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
            <li>• Primary motivation: {results.insights.primaryMotivation}</li>
            <li>• Top skills: {results.insights.topSkills.join(', ')}</li>
            <li>• Recommended timeline: {results.insights.recommendedTimeline}</li>
          </ul>
        </div>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="career-paths" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="career-paths">Career Paths</TabsTrigger>
          <TabsTrigger value="skill-gaps">Skill Analysis</TabsTrigger>
          <TabsTrigger value="learning-path">Learning Path</TabsTrigger>
          <TabsTrigger value="next-steps">Next Steps</TabsTrigger>
        </TabsList>

        {/* Career Paths Tab */}
        <TabsContent value="career-paths" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Career Path List */}
            <div className="lg:col-span-1 space-y-4">
              <h3 className="text-lg font-semibold">Recommended Career Paths</h3>
              {results.careerPathRecommendations.map((career, index) => (
                <Card 
                  key={career.id}
                  className={`cursor-pointer transition-all ${
                    selectedCareerPath === career.id 
                      ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedCareerPath(career.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-600">
                          {career.matchPercentage}%
                        </div>
                        <div className="text-xs text-gray-500">Match</div>
                      </div>
                    </div>
                    <h4 className="font-medium mb-1">{career.name}</h4>
                    <p className="text-sm text-gray-600 mb-2">{career.matchReason}</p>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <DollarSign className="h-3 w-3" />
                      ${career.salaryRange.min.toLocaleString()}-${career.salaryRange.max.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Selected Career Path Details */}
            <div className="lg:col-span-2">
              {selectedCareer && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        {selectedCareer.name}
                      </CardTitle>
                      <div className="flex gap-2">
                        <Button
                          variant={feedback[selectedCareer.id] === true ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleCareerPathFeedback(selectedCareer.id, true)}
                        >
                          <ThumbsUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant={feedback[selectedCareer.id] === false ? "destructive" : "outline"}
                          size="sm"
                          onClick={() => handleCareerPathFeedback(selectedCareer.id, false)}
                        >
                          <ThumbsDown className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Match Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2">Career Overview</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Match Percentage:</span>
                            <span className="font-medium">{selectedCareer.matchPercentage}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Difficulty Level:</span>
                            <Badge variant={
                              selectedCareer.difficultyLevel === 'LOW' ? 'default' :
                              selectedCareer.difficultyLevel === 'MEDIUM' ? 'secondary' : 'destructive'
                            }>
                              {selectedCareer.difficultyLevel}
                            </Badge>
                          </div>
                          <div className="flex justify-between">
                            <span>Transition Time:</span>
                            <span className="font-medium">{selectedCareer.timeToTransition}</span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">Market Information</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Salary Range:</span>
                            <span className="font-medium">
                              ${selectedCareer.salaryRange.min.toLocaleString()}-${selectedCareer.salaryRange.max.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Job Growth:</span>
                            <span className="font-medium text-green-600">{selectedCareer.jobGrowthRate}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Skills Alignment */}
                    <div>
                      <h4 className="font-medium mb-3">Skills Analysis</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h5 className="text-sm font-medium text-green-600 mb-2">Your Strong Skills</h5>
                          <div className="flex flex-wrap gap-1">
                            {selectedCareer.skillAlignment.strongSkills.map(skill => (
                              <Badge key={skill} variant="default" className="text-xs">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div>
                          <h5 className="text-sm font-medium text-orange-600 mb-2">Skills to Develop</h5>
                          <div className="space-y-2">
                            {selectedCareer.skillAlignment.skillGaps.slice(0, 3).map(gap => (
                              <div key={gap.skill} className="flex items-center justify-between">
                                <span className="text-sm">{gap.skill}</span>
                                <Badge variant="outline" className="text-xs">
                                  {gap.priority}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Next Steps */}
                    <div>
                      <h4 className="font-medium mb-3">Recommended Next Steps</h4>
                      <div className="space-y-2">
                        {selectedCareer.nextSteps.map((step, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mt-0.5">
                              {index + 1}
                            </div>
                            <p className="text-sm">{step}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Top Resources */}
                    <div>
                      <h4 className="font-medium mb-3">Recommended Learning Resources</h4>
                      <div className="space-y-3">
                        {selectedCareer.recommendedResources.slice(0, 3).map(resource => (
                          <div key={resource.id} className="border rounded-lg p-3">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1">
                                <h5 className="font-medium text-sm">{resource.title}</h5>
                                <p className="text-xs text-gray-600 mt-1">{resource.description}</p>
                              </div>
                              <div className="flex items-center gap-2 ml-4">
                                <Badge variant="outline" className="text-xs">
                                  {resource.cost}
                                </Badge>
                                <Button size="sm" variant="ghost" asChild>
                                  <a href={resource.url} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-3 w-3" />
                                  </a>
                                </Button>
                              </div>
                            </div>
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {resource.duration}
                              </span>
                              <span className="flex items-center gap-1">
                                <Star className="h-3 w-3" />
                                {resource.averageRating.toFixed(1)}
                              </span>
                              <Badge variant="secondary" className="text-xs">
                                {resource.priority}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Other tabs will be implemented in the next iteration */}
        <TabsContent value="skill-gaps">
          <Card>
            <CardHeader>
              <CardTitle>Skill Gap Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Detailed skill analysis coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="learning-path">
          <Card>
            <CardHeader>
              <CardTitle>Personalized Learning Path</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Custom learning path coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="next-steps">
          <Card>
            <CardHeader>
              <CardTitle>Actionable Next Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Detailed action plan coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
