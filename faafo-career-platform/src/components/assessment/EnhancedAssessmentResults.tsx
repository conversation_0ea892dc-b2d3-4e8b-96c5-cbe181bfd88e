'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Target, 
  TrendingUp, 
  BookOpen, 
  Clock, 
  DollarSign, 
  Star,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  ExternalLink,
  Heart,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { EnhancedAssessmentResults as ResultsType } from '@/lib/enhancedAssessmentService';

interface EnhancedAssessmentResultsProps {
  assessmentId: string;
}

export default function EnhancedAssessmentResults({ assessmentId }: EnhancedAssessmentResultsProps) {
  const [results, setResults] = useState<ResultsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCareerPath, setSelectedCareerPath] = useState<string | null>(null);
  const [feedback, setFeedback] = useState<Record<string, any>>({});

  useEffect(() => {
    fetchEnhancedResults();
  }, [assessmentId]);

  const fetchEnhancedResults = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/assessment/${assessmentId}/enhanced-results`);
      const data = await response.json();

      if (data.success) {
        setResults(data.data);
        setSelectedCareerPath(data.data.careerPathRecommendations[0]?.id || null);
      } else {
        setError(data.error || 'Failed to load enhanced results');
      }
    } catch (err) {
      setError('Failed to load enhanced results');
      console.error('Error fetching enhanced results:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCareerPathFeedback = async (careerPathId: string, isHelpful: boolean) => {
    setFeedback(prev => ({
      ...prev,
      [careerPathId]: isHelpful
    }));

    try {
      await fetch(`/api/assessment/${assessmentId}/enhanced-results`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          careerPathFeedback: {
            [careerPathId]: isHelpful
          }
        })
      });
    } catch (err) {
      console.error('Error saving feedback:', err);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 space-y-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Generating your enhanced assessment results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Card className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-red-700 mb-2">Error Loading Results</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchEnhancedResults}>Try Again</Button>
        </Card>
      </div>
    );
  }

  if (!results) return null;

  const selectedCareer = results.careerPathRecommendations.find(
    cp => cp.id === selectedCareerPath
  );

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Your Enhanced Career Assessment Results
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Personalized insights, career paths, and learning recommendations
        </p>
      </div>

      {/* Overall Readiness Score */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-600" />
            Career Readiness Overview
          </h2>
          <Badge variant={results.insights.scores.readinessScore >= 70 ? 'default' : 'secondary'}>
            {results.insights.scores.readinessScore >= 70 ? 'High Readiness' : 
             results.insights.scores.readinessScore >= 50 ? 'Moderate Readiness' : 'Building Readiness'}
          </Badge>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-1">
              {results.insights.scores.readinessScore}%
            </div>
            <div className="text-sm text-gray-600">Overall Readiness</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">
              {results.careerPathRecommendations.length}
            </div>
            <div className="text-sm text-gray-600">Career Matches</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-1">
              {results.insights.estimatedTransitionTime}
            </div>
            <div className="text-sm text-gray-600">Est. Transition Time</div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h3 className="font-medium mb-2">Key Insights</h3>
          <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
            <li>• Primary motivation: {results.insights.primaryMotivation}</li>
            <li>• Top skills: {results.insights.topSkills.join(', ')}</li>
            <li>• Recommended timeline: {results.insights.recommendedTimeline}</li>
          </ul>
        </div>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="career-paths" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="career-paths">Career Paths</TabsTrigger>
          <TabsTrigger value="skill-gaps">Skill Analysis</TabsTrigger>
          <TabsTrigger value="learning-path">Learning Path</TabsTrigger>
          <TabsTrigger value="next-steps">Next Steps</TabsTrigger>
        </TabsList>

        {/* Career Paths Tab */}
        <TabsContent value="career-paths" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Career Path List */}
            <div className="lg:col-span-1 space-y-4">
              <h3 className="text-lg font-semibold">Recommended Career Paths</h3>
              {results.careerPathRecommendations.map((career, index) => (
                <Card 
                  key={career.id}
                  className={`cursor-pointer transition-all ${
                    selectedCareerPath === career.id 
                      ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedCareerPath(career.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-600">
                          {career.matchPercentage}%
                        </div>
                        <div className="text-xs text-gray-500">Match</div>
                      </div>
                    </div>
                    <h4 className="font-medium mb-1">{career.name}</h4>
                    <p className="text-sm text-gray-600 mb-2">{career.matchReason}</p>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <DollarSign className="h-3 w-3" />
                      ${career.salaryRange.min.toLocaleString()}-${career.salaryRange.max.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Selected Career Path Details */}
            <div className="lg:col-span-2">
              {selectedCareer && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        {selectedCareer.name}
                      </CardTitle>
                      <div className="flex gap-2">
                        <Button
                          variant={feedback[selectedCareer.id] === true ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleCareerPathFeedback(selectedCareer.id, true)}
                        >
                          <ThumbsUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant={feedback[selectedCareer.id] === false ? "destructive" : "outline"}
                          size="sm"
                          onClick={() => handleCareerPathFeedback(selectedCareer.id, false)}
                        >
                          <ThumbsDown className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Match Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2">Career Overview</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Match Percentage:</span>
                            <span className="font-medium">{selectedCareer.matchPercentage}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Difficulty Level:</span>
                            <Badge variant={
                              selectedCareer.difficultyLevel === 'LOW' ? 'default' :
                              selectedCareer.difficultyLevel === 'MEDIUM' ? 'secondary' : 'destructive'
                            }>
                              {selectedCareer.difficultyLevel}
                            </Badge>
                          </div>
                          <div className="flex justify-between">
                            <span>Transition Time:</span>
                            <span className="font-medium">{selectedCareer.timeToTransition}</span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">Market Information</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Salary Range:</span>
                            <span className="font-medium">
                              ${selectedCareer.salaryRange.min.toLocaleString()}-${selectedCareer.salaryRange.max.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Job Growth:</span>
                            <span className="font-medium text-green-600">{selectedCareer.jobGrowthRate}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Skills Alignment */}
                    <div>
                      <h4 className="font-medium mb-3">Skills Analysis</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h5 className="text-sm font-medium text-green-600 mb-2">Your Strong Skills</h5>
                          <div className="flex flex-wrap gap-1">
                            {selectedCareer.skillAlignment.strongSkills.map(skill => (
                              <Badge key={skill} variant="default" className="text-xs">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div>
                          <h5 className="text-sm font-medium text-orange-600 mb-2">Skills to Develop</h5>
                          <div className="space-y-2">
                            {selectedCareer.skillAlignment.skillGaps.slice(0, 3).map(gap => (
                              <div key={gap.skill} className="flex items-center justify-between">
                                <span className="text-sm">{gap.skill}</span>
                                <Badge variant="outline" className="text-xs">
                                  {gap.priority}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Next Steps */}
                    <div>
                      <h4 className="font-medium mb-3">Recommended Next Steps</h4>
                      <div className="space-y-2">
                        {selectedCareer.nextSteps.map((step, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mt-0.5">
                              {index + 1}
                            </div>
                            <p className="text-sm">{step}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Top Resources */}
                    <div>
                      <h4 className="font-medium mb-3">Recommended Learning Resources</h4>
                      <div className="space-y-3">
                        {selectedCareer.recommendedResources.slice(0, 3).map(resource => (
                          <div key={resource.id} className="border rounded-lg p-3">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1">
                                <h5 className="font-medium text-sm">{resource.title}</h5>
                                <p className="text-xs text-gray-600 mt-1">{resource.description}</p>
                              </div>
                              <div className="flex items-center gap-2 ml-4">
                                <Badge variant="outline" className="text-xs">
                                  {resource.cost}
                                </Badge>
                                <Button size="sm" variant="ghost" asChild>
                                  <a href={resource.url} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-3 w-3" />
                                  </a>
                                </Button>
                              </div>
                            </div>
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {resource.duration}
                              </span>
                              <span className="flex items-center gap-1">
                                <Star className="h-3 w-3" />
                                {resource.averageRating.toFixed(1)}
                              </span>
                              <Badge variant="secondary" className="text-xs">
                                {resource.priority}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Skill Gap Analysis Tab */}
        <TabsContent value="skill-gaps" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-orange-600" />
                Skill Gap Analysis
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Overall Skill Assessment */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {results.insights.topSkills.length}
                  </div>
                  <div className="text-sm text-gray-600">Strong Skills</div>
                </div>
                <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600 mb-1">
                    {results.insights.overallSkillGaps.length}
                  </div>
                  <div className="text-sm text-gray-600">Skills to Develop</div>
                </div>
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {results.insights.learningPriorities.length}
                  </div>
                  <div className="text-sm text-gray-600">Learning Priorities</div>
                </div>
              </div>

              {/* Skills by Career Path */}
              {selectedCareer && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Skills for {selectedCareer.name}</h3>

                  {/* Strong Skills */}
                  <div>
                    <h4 className="font-medium text-green-600 mb-3 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      Your Strong Skills
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {selectedCareer.skillAlignment.strongSkills.map(skill => (
                        <div key={skill} className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <span className="font-medium">{skill}</span>
                          <div className="flex items-center gap-2">
                            <Progress value={85} className="w-20" />
                            <span className="text-sm text-green-600">85%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Skill Gaps */}
                  <div>
                    <h4 className="font-medium text-orange-600 mb-3 flex items-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      Skills to Develop
                    </h4>
                    <div className="space-y-3">
                      {selectedCareer.skillAlignment.skillGaps.map(gap => (
                        <div key={gap.skill} className="p-4 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <h5 className="font-medium">{gap.skill}</h5>
                              <Badge variant={
                                gap.priority === 'HIGH' ? 'destructive' :
                                gap.priority === 'MEDIUM' ? 'secondary' : 'outline'
                              }>
                                {gap.priority}
                              </Badge>
                            </div>
                            <div className="text-sm text-gray-500">
                              {gap.estimatedLearningTime}
                            </div>
                          </div>

                          <div className="flex items-center gap-4 mb-2">
                            <div className="flex-1">
                              <div className="flex justify-between text-sm mb-1">
                                <span>Current Level</span>
                                <span>Target Level</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Progress value={(gap.currentLevel / 5) * 100} className="flex-1" />
                                <ArrowRight className="h-4 w-4 text-gray-400" />
                                <Progress value={(gap.requiredLevel / 5) * 100} className="flex-1" />
                              </div>
                              <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Level {gap.currentLevel}</span>
                                <span>Level {gap.requiredLevel}</span>
                              </div>
                            </div>
                          </div>

                          <div className="text-sm text-gray-600">
                            Gap: {gap.requiredLevel - gap.currentLevel} levels to bridge
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Learning Path Tab */}
        <TabsContent value="learning-path" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-purple-600" />
                Personalized Learning Path
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Learning Path Overview */}
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">{results.learningPath.title}</h3>
                <p className="text-gray-600 mb-4">{results.learningPath.description}</p>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{results.learningPath.estimatedDuration}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Target className="h-4 w-4" />
                    <span>{results.learningPath.phases.length} Phases</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CheckCircle className="h-4 w-4" />
                    <span>{results.learningPath.milestones.length} Milestones</span>
                  </div>
                </div>
              </div>

              {/* Learning Phases */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Learning Phases</h3>
                <div className="space-y-4">
                  {results.learningPath.phases.length > 0 ? (
                    results.learningPath.phases.map((phase, index) => (
                      <div key={phase.phase} className="border rounded-lg p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="bg-purple-100 text-purple-600 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                            {phase.phase}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium">{phase.title}</h4>
                            <p className="text-sm text-gray-600">{phase.description}</p>
                          </div>
                          <div className="text-sm text-gray-500">
                            {phase.duration}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="text-sm font-medium mb-2">Skills Covered</h5>
                            <div className="flex flex-wrap gap-1">
                              {phase.skills.map(skill => (
                                <Badge key={skill} variant="outline" className="text-xs">
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div>
                            <h5 className="text-sm font-medium mb-2">Resources ({phase.resources.length})</h5>
                            <div className="text-sm text-gray-600">
                              {phase.resources.length > 0
                                ? `${phase.resources.length} curated resources available`
                                : 'Resources will be recommended based on your progress'
                              }
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Your personalized learning path is being generated...</p>
                      <p className="text-sm mt-2">This will be available once you select a career path focus.</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Milestones */}
              {results.learningPath.milestones.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Learning Milestones</h3>
                  <div className="space-y-3">
                    {results.learningPath.milestones.map((milestone, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mt-1">
                          {milestone.estimatedWeek}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">{milestone.title}</h4>
                          <p className="text-sm text-gray-600 mb-2">{milestone.description}</p>
                          <div className="flex flex-wrap gap-1">
                            {milestone.skills.map(skill => (
                              <Badge key={skill} variant="secondary" className="text-xs">
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="text-xs text-gray-500">
                          Week {milestone.estimatedWeek}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Next Steps Tab */}
        <TabsContent value="next-steps" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowRight className="h-5 w-5 text-green-600" />
                Actionable Next Steps
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Immediate Actions */}
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <div className="bg-red-100 text-red-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                    !
                  </div>
                  Immediate Actions (This Week)
                </h3>
                <div className="space-y-3">
                  {results.nextSteps
                    .filter(step => step.category === 'IMMEDIATE')
                    .map((step, index) => (
                      <div key={index} className="flex items-start gap-3 p-4 border-l-4 border-red-500 bg-red-50 dark:bg-red-900/20 rounded-r-lg">
                        <div className="bg-red-100 text-red-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mt-1">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-red-800 dark:text-red-200">{step.title}</h4>
                          <p className="text-sm text-red-700 dark:text-red-300 mb-2">{step.description}</p>
                          <div className="flex items-center gap-2 text-xs">
                            <Clock className="h-3 w-3" />
                            <span>{step.estimatedTime}</span>
                            <Badge variant="destructive" className="text-xs">
                              {step.priority}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  {results.nextSteps.filter(step => step.category === 'IMMEDIATE').length === 0 && (
                    <div className="text-center py-4 text-gray-500">
                      <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                      <p>Great! No immediate urgent actions needed.</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Short-term Actions */}
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <div className="bg-orange-100 text-orange-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                    2W
                  </div>
                  Short-term Goals (Next 2-4 Weeks)
                </h3>
                <div className="space-y-3">
                  {results.nextSteps
                    .filter(step => step.category === 'SHORT_TERM')
                    .map((step, index) => (
                      <div key={index} className="flex items-start gap-3 p-4 border-l-4 border-orange-500 bg-orange-50 dark:bg-orange-900/20 rounded-r-lg">
                        <div className="bg-orange-100 text-orange-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mt-1">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-orange-800 dark:text-orange-200">{step.title}</h4>
                          <p className="text-sm text-orange-700 dark:text-orange-300 mb-2">{step.description}</p>
                          <div className="flex items-center gap-2 text-xs">
                            <Clock className="h-3 w-3" />
                            <span>{step.estimatedTime}</span>
                            <Badge variant="secondary" className="text-xs">
                              {step.priority}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Long-term Actions */}
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                    3M
                  </div>
                  Long-term Strategy (Next 3-6 Months)
                </h3>
                <div className="space-y-3">
                  {results.nextSteps
                    .filter(step => step.category === 'LONG_TERM')
                    .map((step, index) => (
                      <div key={index} className="flex items-start gap-3 p-4 border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 rounded-r-lg">
                        <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mt-1">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-blue-800 dark:text-blue-200">{step.title}</h4>
                          <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">{step.description}</p>
                          <div className="flex items-center gap-2 text-xs">
                            <Clock className="h-3 w-3" />
                            <span>{step.estimatedTime}</span>
                            <Badge variant="outline" className="text-xs">
                              {step.priority}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Action Summary */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">Your Action Plan Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600 mb-1">
                      {results.nextSteps.filter(step => step.category === 'IMMEDIATE').length}
                    </div>
                    <div className="text-sm text-gray-600">Immediate Actions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600 mb-1">
                      {results.nextSteps.filter(step => step.category === 'SHORT_TERM').length}
                    </div>
                    <div className="text-sm text-gray-600">Short-term Goals</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-1">
                      {results.nextSteps.filter(step => step.category === 'LONG_TERM').length}
                    </div>
                    <div className="text-sm text-gray-600">Long-term Strategy</div>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <p className="text-sm text-gray-600">
                    <strong>Recommended approach:</strong> Focus on immediate actions first, then gradually work on short-term goals while keeping long-term strategy in mind. Review and adjust your plan monthly based on progress.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
