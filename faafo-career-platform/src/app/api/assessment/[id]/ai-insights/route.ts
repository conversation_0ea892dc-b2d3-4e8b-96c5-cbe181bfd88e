import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { aiEnhancedAssessmentService } from '@/lib/aiEnhancedAssessmentService';
import { EnhancedAssessmentService } from '@/lib/enhancedAssessmentService';
import { AssessmentResponse } from '@/lib/assessmentScoring';
import { cache } from '@/lib/cache';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: assessmentId } = await params;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!assessmentId) {
      return NextResponse.json(
        { success: false, error: 'Assessment ID is required' },
        { status: 400 }
      );
    }

    // Check if AI services are available
    if (!process.env.GOOGLE_GEMINI_API_KEY) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'AI services are not configured',
          fallback: true 
        },
        { status: 503 }
      );
    }

    // Check cache first
    const cacheKey = `ai_insights:${assessmentId}:${session.user.id}`;
    const cachedInsights = await cache.get(cacheKey);
    
    if (cachedInsights && typeof cachedInsights === 'string') {
      try {
        const parsed = JSON.parse(cachedInsights);
        return NextResponse.json({
          success: true,
          data: parsed,
          cached: true,
          message: 'AI insights retrieved from cache'
        });
      } catch (error) {
        console.error('Error parsing cached AI insights:', error);
      }
    }

    // Verify assessment belongs to user
    const assessment = await prisma.assessment.findFirst({
      where: {
        id: assessmentId,
        userId: session.user.id
      },
      include: {
        responses: true
      }
    });

    if (!assessment) {
      return NextResponse.json(
        { success: false, error: 'Assessment not found or access denied' },
        { status: 404 }
      );
    }

    if (assessment.status !== 'COMPLETED') {
      return NextResponse.json(
        { success: false, error: 'Assessment is not completed' },
        { status: 400 }
      );
    }

    // Convert assessment responses to the expected format
    const responseData: AssessmentResponse = {};
    assessment.responses.forEach(response => {
      try {
        const value = typeof response.answerValue === 'string' 
          ? JSON.parse(response.answerValue) 
          : response.answerValue;
        responseData[response.questionKey] = value as string | string[] | number | null;
      } catch {
        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
      }
    });

    // Generate enhanced results first
    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
      assessmentId,
      responseData
    );

    // Generate AI insights
    console.log(`Generating AI insights for user ${session.user.id}, assessment ${assessmentId}`);
    
    const aiInsights = await aiEnhancedAssessmentService.generateAIInsights(
      assessmentId,
      responseData,
      enhancedResults.insights,
      enhancedResults.careerPathRecommendations,
      session.user.id
    );

    // Cache the results for 24 hours
    await cache.set(cacheKey, JSON.stringify(aiInsights), 86400);

    console.log(`AI insights generated successfully for user ${session.user.id}, assessment ${assessmentId}`);

    return NextResponse.json({
      success: true,
      data: aiInsights,
      cached: false,
      message: 'AI insights generated successfully'
    });

  } catch (error) {
    console.error('Error generating AI insights:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate AI insights',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}

// POST endpoint to regenerate AI insights with specific focus areas
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: assessmentId } = await params;
    const body = await request.json();

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!process.env.GOOGLE_GEMINI_API_KEY) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'AI services are not configured' 
        },
        { status: 503 }
      );
    }

    const {
      focusAreas = [],
      analysisDepth = 'standard',
      includeMarketData = true,
      personalityFocus = true
    } = body;

    // Verify assessment belongs to user
    const assessment = await prisma.assessment.findFirst({
      where: {
        id: assessmentId,
        userId: session.user.id
      },
      include: {
        responses: true
      }
    });

    if (!assessment) {
      return NextResponse.json(
        { success: false, error: 'Assessment not found or access denied' },
        { status: 404 }
      );
    }

    // Convert assessment responses
    const responseData: AssessmentResponse = {};
    assessment.responses.forEach(response => {
      try {
        const value = typeof response.answerValue === 'string' 
          ? JSON.parse(response.answerValue) 
          : response.answerValue;
        responseData[response.questionKey] = value as string | string[] | number | null;
      } catch {
        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
      }
    });

    // Add user preferences to response data for AI analysis
    (responseData as any).ai_preferences = {
      focusAreas,
      analysisDepth,
      includeMarketData,
      personalityFocus
    };

    // Generate enhanced results with preferences
    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
      assessmentId,
      responseData
    );

    // Generate AI insights with custom focus
    const aiInsights = await aiEnhancedAssessmentService.generateAIInsights(
      assessmentId,
      responseData,
      enhancedResults.insights,
      enhancedResults.careerPathRecommendations,
      session.user.id
    );

    // Cache with custom key for preferences
    const customCacheKey = `ai_insights:${assessmentId}:${session.user.id}:custom:${Date.now()}`;
    await cache.set(customCacheKey, JSON.stringify(aiInsights), 3600); // 1 hour cache

    console.log(`Custom AI insights generated for user ${session.user.id}, assessment ${assessmentId}`);

    return NextResponse.json({
      success: true,
      data: aiInsights,
      cached: false,
      message: 'Custom AI insights generated successfully',
      preferences: {
        focusAreas,
        analysisDepth,
        includeMarketData,
        personalityFocus
      }
    });

  } catch (error) {
    console.error('Error generating custom AI insights:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate custom AI insights',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}

// DELETE endpoint to clear AI insights cache
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: assessmentId } = await params;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Clear cache for this assessment
    const cacheKey = `ai_insights:${assessmentId}:${session.user.id}`;
    await cache.del(cacheKey);

    console.log(`AI insights cache cleared for user ${session.user.id}, assessment ${assessmentId}`);

    return NextResponse.json({
      success: true,
      message: 'AI insights cache cleared successfully'
    });

  } catch (error) {
    console.error('Error clearing AI insights cache:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to clear AI insights cache'
      },
      { status: 500 }
    );
  }
}
