import { AssessmentResponse, AssessmentInsights, SkillGap, CareerPathAnalysis } from './assessmentScoring';
import { generateAssessmentInsights } from './assessmentScoring';
import prisma from './prisma';

export interface EnhancedAssessmentResults {
  insights: AssessmentInsights;
  careerPathRecommendations: CareerPathRecommendation[];
  learningPath: LearningPathRecommendation;
  skillDevelopmentPlan: SkillDevelopmentPlan;
  nextSteps: ActionableStep[];
}

export interface CareerPathRecommendation {
  id: string;
  name: string;
  slug: string;
  matchPercentage: number;
  matchReason: string;
  salaryRange: {
    min: number;
    max: number;
    currency: string;
  };
  jobGrowthRate: string;
  skillAlignment: {
    strongSkills: string[];
    skillGaps: SkillGap[];
  };
  timeToTransition: string;
  difficultyLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  recommendedResources: LearningResourceRecommendation[];
  nextSteps: string[];
}

export interface LearningResourceRecommendation {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  skillLevel: string;
  duration: string;
  cost: string;
  format: string;
  averageRating: number;
  relevanceScore: number;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  skillsAddressed: string[];
}

export interface LearningPathRecommendation {
  title: string;
  description: string;
  estimatedDuration: string;
  phases: LearningPhase[];
  milestones: Milestone[];
}

export interface LearningPhase {
  phase: number;
  title: string;
  description: string;
  duration: string;
  skills: string[];
  resources: LearningResourceRecommendation[];
  prerequisites: string[];
}

export interface Milestone {
  title: string;
  description: string;
  estimatedWeek: number;
  skills: string[];
  deliverables: string[];
}

export interface SkillDevelopmentPlan {
  prioritySkills: PrioritySkill[];
  learningSchedule: LearningScheduleItem[];
  estimatedTimeToCompetency: string;
}

export interface PrioritySkill {
  skill: string;
  currentLevel: number;
  targetLevel: number;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  estimatedLearningTime: string;
  resources: LearningResourceRecommendation[];
  prerequisites: string[];
}

export interface LearningScheduleItem {
  week: number;
  focus: string;
  skills: string[];
  resources: string[];
  timeCommitment: string;
}

export interface ActionableStep {
  category: 'IMMEDIATE' | 'SHORT_TERM' | 'LONG_TERM';
  title: string;
  description: string;
  estimatedTime: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  resources: string[];
  dependencies: string[];
}

export class EnhancedAssessmentService {
  static async generateEnhancedResults(
    assessmentId: string,
    responses: AssessmentResponse
  ): Promise<EnhancedAssessmentResults> {
    // Generate base insights
    const insights = generateAssessmentInsights(responses);
    
    // Get career path recommendations with detailed analysis
    const careerPathRecommendations = await this.generateCareerPathRecommendations(
      assessmentId, 
      responses, 
      insights
    );
    
    // Generate personalized learning path
    const learningPath = await this.generateLearningPath(
      responses, 
      insights, 
      careerPathRecommendations
    );
    
    // Create skill development plan
    const skillDevelopmentPlan = await this.generateSkillDevelopmentPlan(
      responses, 
      insights, 
      careerPathRecommendations
    );
    
    // Generate actionable next steps
    const nextSteps = this.generateActionableSteps(
      insights, 
      careerPathRecommendations, 
      skillDevelopmentPlan
    );
    
    return {
      insights,
      careerPathRecommendations,
      learningPath,
      skillDevelopmentPlan,
      nextSteps
    };
  }

  private static async generateCareerPathRecommendations(
    assessmentId: string,
    responses: AssessmentResponse,
    insights: AssessmentInsights
  ): Promise<CareerPathRecommendation[]> {
    // Get career path suggestions from existing service
    const { getCareerPathSuggestions } = await import('./suggestionService');
    const suggestions = await getCareerPathSuggestions(assessmentId);
    
    // Enhance each suggestion with detailed analysis
    const enhancedRecommendations: CareerPathRecommendation[] = [];
    
    for (const suggestion of suggestions.slice(0, 5)) { // Top 5 recommendations
      const careerPath = suggestion.careerPath;
      
      // Calculate skill gaps for this career path
      const skillGaps = await this.calculateSkillGaps(
        careerPath.id,
        insights.topSkills,
        responses
      );
      
      // Get recommended resources for this career path
      const recommendedResources = await this.getCareerPathResources(
        careerPath.id,
        skillGaps
      );
      
      // Generate next steps
      const nextSteps = this.generateCareerPathNextSteps(
        careerPath,
        skillGaps,
        insights
      );
      
      enhancedRecommendations.push({
        id: careerPath.id,
        name: careerPath.name,
        slug: careerPath.slug,
        matchPercentage: Math.round(suggestion.score),
        matchReason: suggestion.matchReason || 'Good match based on your assessment responses',
        salaryRange: this.getSalaryRange(careerPath.name),
        jobGrowthRate: this.getJobGrowthRate(careerPath.name),
        skillAlignment: {
          strongSkills: this.getAlignedSkills(careerPath, insights.topSkills),
          skillGaps
        },
        timeToTransition: this.estimateTransitionTime(skillGaps, insights),
        difficultyLevel: this.assessDifficultyLevel(skillGaps, insights),
        recommendedResources,
        nextSteps
      });
    }
    
    return enhancedRecommendations;
  }

  private static async calculateSkillGaps(
    careerPathId: string,
    userSkills: string[],
    responses: AssessmentResponse
  ): Promise<SkillGap[]> {
    // Get required skills for this career path
    const careerPath = await prisma.careerPath.findUnique({
      where: { id: careerPathId },
      include: {
        skills: true
      }
    });
    
    if (!careerPath) return [];
    
    const skillGaps: SkillGap[] = [];
    
    for (const skill of careerPath.skills) {
      const userHasSkill = userSkills.includes(skill.name.toLowerCase());
      const currentLevel = userHasSkill ? 3 : 1; // Simplified logic
      const requiredLevel = 4; // Most careers require intermediate to advanced
      
      if (currentLevel < requiredLevel) {
        skillGaps.push({
          skill: skill.name,
          currentLevel,
          requiredLevel,
          priority: this.getSkillPriority(skill.name, careerPath.name),
          estimatedLearningTime: this.estimateSkillLearningTime(
            skill.name,
            requiredLevel - currentLevel
          ),
          recommendedResources: [] // Will be populated later
        });
      }
    }
    
    return skillGaps.sort((a, b) => {
      const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  private static async getCareerPathResources(
    careerPathId: string,
    skillGaps: SkillGap[]
  ): Promise<LearningResourceRecommendation[]> {
    const resources = await prisma.learningResource.findMany({
      where: {
        isActive: true,
        careerPaths: {
          some: {
            id: careerPathId
          }
        }
      },
      include: {
        ratings: {
          select: {
            rating: true
          }
        }
      },
      take: 10
    });
    
    return resources.map(resource => ({
      id: resource.id,
      title: resource.title,
      description: resource.description,
      url: resource.url,
      type: resource.type,
      category: resource.category,
      skillLevel: resource.skillLevel,
      duration: resource.duration || 'Not specified',
      cost: resource.cost,
      format: resource.format,
      averageRating: resource.ratings.length > 0 
        ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
        : 0,
      relevanceScore: this.calculateResourceRelevance(resource, skillGaps),
      priority: this.getResourcePriority(resource, skillGaps),
      skillsAddressed: this.getResourceSkills(resource, skillGaps)
    })).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  // Helper methods for calculations
  private static getSalaryRange(careerPathName: string) {
    // Simplified salary data - in production, this would come from a salary API
    const salaryData: Record<string, { min: number; max: number }> = {
      'Software Developer': { min: 70000, max: 150000 },
      'Data Scientist': { min: 80000, max: 160000 },
      'Product Manager': { min: 90000, max: 180000 },
      'UX Designer': { min: 65000, max: 130000 },
      'Digital Marketer': { min: 50000, max: 100000 }
    };
    
    return {
      ...salaryData[careerPathName] || { min: 50000, max: 100000 },
      currency: 'USD'
    };
  }

  private static getJobGrowthRate(careerPathName: string): string {
    // Simplified job growth data
    const growthData: Record<string, string> = {
      'Software Developer': '22% (Much faster than average)',
      'Data Scientist': '35% (Much faster than average)',
      'Product Manager': '19% (Much faster than average)',
      'UX Designer': '13% (Faster than average)',
      'Digital Marketer': '10% (Faster than average)'
    };
    
    return growthData[careerPathName] || '8% (As fast as average)';
  }

  private static getAlignedSkills(careerPath: any, userSkills: string[]): string[] {
    // Simplified skill alignment logic
    return userSkills.slice(0, 3); // Return top 3 user skills as aligned
  }

  private static estimateTransitionTime(skillGaps: SkillGap[], insights: AssessmentInsights): string {
    const totalGaps = skillGaps.length;
    const urgency = insights.scores.urgencyLevel;
    
    if (totalGaps <= 2 && urgency >= 4) return '3-6 months';
    if (totalGaps <= 4 && urgency >= 3) return '6-12 months';
    if (totalGaps <= 6) return '12-18 months';
    return '18-24 months';
  }

  private static assessDifficultyLevel(skillGaps: SkillGap[], insights: AssessmentInsights): 'LOW' | 'MEDIUM' | 'HIGH' {
    const highPriorityGaps = skillGaps.filter(gap => gap.priority === 'HIGH').length;
    const readinessScore = insights.scores.readinessScore;
    
    if (highPriorityGaps <= 1 && readinessScore >= 70) return 'LOW';
    if (highPriorityGaps <= 3 && readinessScore >= 50) return 'MEDIUM';
    return 'HIGH';
  }

  private static getSkillPriority(skillName: string, careerPathName: string): 'HIGH' | 'MEDIUM' | 'LOW' {
    // Simplified priority logic - in production, this would be more sophisticated
    const coreSkills = ['Programming', 'Data Analysis', 'Project Management', 'Communication'];
    return coreSkills.some(core => skillName.includes(core)) ? 'HIGH' : 'MEDIUM';
  }

  private static estimateSkillLearningTime(skillName: string, levelGap: number): string {
    const baseTime = levelGap * 4; // 4 weeks per level
    return `${baseTime}-${baseTime + 4} weeks`;
  }

  private static calculateResourceRelevance(resource: any, skillGaps: SkillGap[]): number {
    // Simplified relevance calculation
    return Math.random() * 100; // In production, this would be more sophisticated
  }

  private static getResourcePriority(resource: any, skillGaps: SkillGap[]): 'HIGH' | 'MEDIUM' | 'LOW' {
    return resource.cost === 'FREE' ? 'HIGH' : 'MEDIUM';
  }

  private static getResourceSkills(resource: any, skillGaps: SkillGap[]): string[] {
    return skillGaps.slice(0, 2).map(gap => gap.skill);
  }

  private static generateCareerPathNextSteps(
    careerPath: any,
    skillGaps: SkillGap[],
    insights: AssessmentInsights
  ): string[] {
    const steps = [
      'Complete a skills assessment to identify specific gaps',
      'Start with foundational courses in your priority skill areas',
      'Build a portfolio project to demonstrate your capabilities'
    ];
    
    if (skillGaps.length > 3) {
      steps.push('Consider a structured bootcamp or certification program');
    }
    
    if (insights.scores.urgencyLevel >= 4) {
      steps.unshift('Begin networking in your target industry immediately');
    }
    
    return steps;
  }

  // Additional methods will be implemented in the next iteration
  private static async generateLearningPath(
    responses: AssessmentResponse,
    insights: AssessmentInsights,
    careerPathRecommendations: CareerPathRecommendation[]
  ): Promise<LearningPathRecommendation> {
    // Placeholder implementation
    return {
      title: 'Personalized Learning Path',
      description: 'A customized learning journey based on your assessment results',
      estimatedDuration: '6-12 months',
      phases: [],
      milestones: []
    };
  }

  private static async generateSkillDevelopmentPlan(
    responses: AssessmentResponse,
    insights: AssessmentInsights,
    careerPathRecommendations: CareerPathRecommendation[]
  ): Promise<SkillDevelopmentPlan> {
    // Placeholder implementation
    return {
      prioritySkills: [],
      learningSchedule: [],
      estimatedTimeToCompetency: '6-12 months'
    };
  }

  private static generateActionableSteps(
    insights: AssessmentInsights,
    careerPathRecommendations: CareerPathRecommendation[],
    skillDevelopmentPlan: SkillDevelopmentPlan
  ): ActionableStep[] {
    // Placeholder implementation
    return [];
  }
}
